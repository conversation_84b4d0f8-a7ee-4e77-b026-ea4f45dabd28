package com.chic.dea.infrastructure.sql;

import com.alibaba.druid.pool.DruidDataSource;
import com.chic.dea.apis.model.vo.DataSourceVO;

/**
 * sql 构造器
 * <AUTHOR>
 * @classname SqlBuilder
 * @date 2024/3/21 18:09
 */
public interface SqlBuilder {

    /**
     * 构建buildDataSource
     * @param dataSourceVo
     * @return com.alibaba.druid.pool.DruidDataSource
     * <AUTHOR>
     * @date 2024/3/21 20:06
     */
    DruidDataSource buildDataSource(DataSourceVO dataSourceVo);


    }
